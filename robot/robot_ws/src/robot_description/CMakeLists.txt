cmake_minimum_required(VERSION 3.8)
project(robot_description)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()

# Install launch files so they are available under
# install/<pkg>/share/<pkg>/launch for `ros2 launch`.
if(EXISTS ${CMAKE_SOURCE_DIR}/launch)
  install(DIRECTORY launch/
    DESTINATION share/${PROJECT_NAME}/launch
    FILES_MATCHING PATTERN "*.py"
  )
endif()

# Install URDF / xacro files so they are available at
# install/<pkg>/share/<pkg>/urdf/... for runtime xacro calls
if(EXISTS ${CMAKE_SOURCE_DIR}/urdf)
  install(DIRECTORY urdf/
    DESTINATION share/${PROJECT_NAME}/urdf
    FILES_MATCHING
      PATTERN "*.xacro"
      PATTERN "*.urdf"
      PATTERN "*.dae"
      PATTERN "*.stl"
  )
endif()
