[0.067s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'robot_description']
[0.067s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['robot_description'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7adc179674c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7adc17aacf10>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7adc17aacf10>>)
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.217s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/robot/robot_ws'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.233s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.234s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.234s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.234s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.234s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.234s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.249s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 372 installed packages in /opt/ros/humble
[0.250s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.274s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.274s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot/robot_ws/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot/robot_ws/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/robot/robot_ws/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.274s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.275s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.275s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot/robot_ws/src/robot_description' with build type 'ament_cmake'
[0.275s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot/robot_ws/src/robot_description'
[0.277s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.277s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.277s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.282s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.315s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.324s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.333s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.333s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.335s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.335s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.335s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.336s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.336s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.336s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.337s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.337s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.338s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.338s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.338s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.339s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.339s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.340s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.340s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.340s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.340s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.340s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.340s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.341s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.341s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.341s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.342s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.342s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.342s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.342s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.342s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.345s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.345s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.345s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.355s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.356s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.ps1'
[0.357s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_ps1.py'
[0.358s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.ps1'
[0.358s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.sh'
[0.358s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_sh.py'
[0.359s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.sh'
[0.359s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.bash'
[0.360s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.bash'
[0.360s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.zsh'
[0.360s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.zsh'
