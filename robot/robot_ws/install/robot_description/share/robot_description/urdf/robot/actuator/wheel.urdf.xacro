<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    
    <!-- 轮子参数定义 -->
    <xacro:property name="wheel_radius" value="0.08"/> <!-- 轮子半径 -->
    <xacro:property name="wheel_width" value="0.04"/> <!-- 轮子宽度 -->
    <xacro:property name="wheel_mass" value="2.0"/> <!-- 轮子质量 -->
    <xacro:property name="wheel_offset_x" value="0.2"/> <!-- 轮子前后间距的一半 -->
    <xacro:property name="wheel_offset_y" value="0.18"/> <!-- 轮子左右间距的一半 -->
    <xacro:property name="wheel_offset_z" value="0.0"/> <!-- 轮子相对于base_link的高度偏移 -->
    
    <!-- 轮子宏定义 -->
    <xacro:macro name="wheel" params="prefix reflect_x reflect_y">
        <!-- 轮子link -->
        <link name="${prefix}_wheel">
            <inertial>
                <origin xyz="0 0 0" rpy="0 0 0"/>
                <mass value="${wheel_mass}"/>
                <inertia ixx="${wheel_mass/12 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                         ixy="0" ixz="0"
                         iyy="${wheel_mass/12 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                         iyz="0"
                         izz="${wheel_mass/2 * wheel_radius*wheel_radius}"/>
            </inertial>
            <visual>
                <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
                </geometry>
                <material name="black">
                    <color rgba="0.2 0.2 0.2 1"/>
                </material>
            </visual>
            <collision>
                <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
                </geometry>
            </collision>
        </link>
        
        <!-- 轮子关节 - 只能绕Y轴旋转（滚动），不能转向 -->
        <joint name="${prefix}_wheel_joint" type="continuous">
            <parent link="base_link"/>
            <child link="${prefix}_wheel"/>
            <origin xyz="${reflect_x * wheel_offset_x} ${reflect_y * wheel_offset_y} ${wheel_offset_z}" 
                    rpy="0 0 0"/>
            <axis xyz="0 1 0"/> <!-- 只能绕Y轴旋转 -->
            <dynamics damping="0.2" friction="0.1"/>
        </joint>
        
        <!-- Gazebo轮子属性 -->
        <gazebo reference="${prefix}_wheel">
            <material>Gazebo/Black</material>
            <mu1>1.0</mu1> <!-- 摩擦系数 -->
            <mu2>1.0</mu2>
            <kp>1000000.0</kp>
            <kd>1.0</kd>
        </gazebo>
        
        <!-- 传动系统 -->
        <transmission name="${prefix}_wheel_transmission">
            <type>transmission_interface/SimpleTransmission</type>
            <joint name="${prefix}_wheel_joint">
                <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
            </joint>
            <actuator name="${prefix}_wheel_motor">
                <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
                <mechanicalReduction>1</mechanicalReduction>
            </actuator>
        </transmission>
    </xacro:macro>
    
    <!-- 实例化四个轮子 -->
    <!-- 左前轮 -->
    <xacro:wheel prefix="front_left" reflect_x="1" reflect_y="1"/>
    
    <!-- 右前轮 -->
    <xacro:wheel prefix="front_right" reflect_x="1" reflect_y="-1"/>
    
    <!-- 左后轮 -->
    <xacro:wheel prefix="rear_left" reflect_x="-1" reflect_y="1"/>
    
    <!-- 右后轮 -->
    <xacro:wheel prefix="rear_right" reflect_x="-1" reflect_y="-1"/>
    
    <!-- Gazebo差速驱动插件 -->
    <gazebo>
        <plugin name="differential_drive_controller" filename="libgazebo_ros_diff_drive.so">
            <!-- 插件参数 -->
            <updateRate>50</updateRate>
            <leftJoint>front_left_wheel_joint</leftJoint>
            <rightJoint>front_right_wheel_joint</rightJoint>
            <leftJoint>rear_left_wheel_joint</leftJoint>
            <rightJoint>rear_right_wheel_joint</rightJoint>
            <wheelSeparation>${2 * wheel_offset_y}</wheelSeparation>
            <wheelDiameter>${2 * wheel_radius}</wheelDiameter>
            <wheelAcceleration>1.0</wheelAcceleration>
            <wheelTorque>20</wheelTorque>
            <commandTopic>cmd_vel</commandTopic>
            <odometryTopic>odom</odometryTopic>
            <odometryFrame>odom</odometryFrame>
            <robotBaseFrame>base_footprint</robotBaseFrame>
            <odometrySource>world</odometrySource>
            <publishWheelTF>true</publishWheelTF>
            <publishOdom>true</publishOdom>
            <publishWheelJointState>true</publishWheelJointState>
            <legacyMode>false</legacyMode>
        </plugin>
    </gazebo>
    
</robot>
