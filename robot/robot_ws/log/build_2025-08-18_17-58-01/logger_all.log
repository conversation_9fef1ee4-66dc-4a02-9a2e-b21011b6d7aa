[0.063s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.063s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fb4db40b310>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fb4db5a4d60>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fb4db5a4d60>>)
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.172s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.172s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/robot/robot_ws'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.199s] WARNING:colcon.colcon_core.prefix_path.colcon:The path '/home/<USER>/robot/robot_ws/src/install' in the environment variable COLCON_PREFIX_PATH doesn't exist
[0.199s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/robot/robot_ws/src/install/robot_description' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.199s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/robot/robot_ws/src/install/robot_description' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.200s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/robot/robot_ws/install
[0.201s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 372 installed packages in /opt/ros/humble
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.225s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot/robot_ws/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot/robot_ws/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/robot/robot_ws/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.226s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.227s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot/robot_ws/src/robot_description' with build type 'ament_cmake'
[0.227s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot/robot_ws/src/robot_description'
[0.228s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.228s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.228s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.235s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.264s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.273s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.280s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.280s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.282s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.282s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.282s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.283s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.284s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.284s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.284s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.285s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.285s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.285s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.286s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.286s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.286s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.286s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.286s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.287s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.287s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.288s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.288s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.288s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.288s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.288s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.288s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.288s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.288s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.292s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.292s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.292s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.301s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.301s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.ps1'
[0.302s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_ps1.py'
[0.303s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.ps1'
[0.303s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.sh'
[0.303s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_sh.py'
[0.304s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.sh'
[0.304s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.bash'
[0.304s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.bash'
[0.305s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.zsh'
[0.305s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.zsh'
